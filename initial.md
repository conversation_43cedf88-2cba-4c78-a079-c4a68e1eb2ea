# 🤖 Tribe LINE Bot 專案 - Claude Code 初始化文件

## 🎯 專案概述

**tribe-line-bot** 是一個基於 Google Apps Script 和 Gemini AI 的現代化 LINE Bot 系統，採用模組化架構設計，版本 v2.6.0。

### 核心特色
- **🧠 AI-First 架構**：深度整合 Gemini AI 模型家族
- **📦 模組化設計**：30+ 功能模組，易於維護和擴展
- **⚡ 高效響應**：智能頻率控制和被動調用鏈技術
- **🎨 多媒體支援**：圖片、語音、文件全方位處理
- **👥 群組功能**：完整的聊天記錄和 AI 分析

## 🏗️ 技術架構

### 主要技術棧
- **運行環境**：Google Apps Script (JavaScript)
- **數據存儲**：Google Sheets
- **文件存儲**：Google Drive
- **AI 服務**：Gemini AI API
- **外部 API**：LINE Bot API, YouTube API, Google Search API

### 專案結構
```
tribe-line-bot/
├── 🔧 核心系統
│   ├── core_main.gs              # 主要業務邏輯和程式進入點
│   ├── config_core.gs            # 工作表管理和配置系統
│   ├── config_model.gs           # AI 模型配置管理
│   ├── core_utils.gs             # 通用工具函數
│   ├── core_helpers.gs           # 輔助函數
│   ├── core_feature_toggle.gs    # 功能開關管理
│   └── core_module_manager.gs    # 模組管理器
├── 📡 LINE 整合
│   ├── modules_line_webhook.gs        # Webhook 處理 (doPost)
│   ├── modules_line_processor_core.gs # 核心處理器
│   ├── modules_line_processor_cmd.gs  # 指令處理器
│   ├── modules_line_processor_intent.gs # 意圖識別
│   ├── modules_line_processor_story.gs # 故事處理
│   ├── modules_line_processor_utils.gs # 處理工具
│   └── modules_line_media.gs         # 媒體處理
├── 🤖 AI 功能
│   ├── modules_ai_processor.gs       # AI 處理器
│   ├── modules_ai_features.gs        # AI 功能整合
│   └── modules_ai_prompts.gs         # 提示詞管理
├── 🎨 多媒體處理
│   ├── modules_image_core.gs         # 圖片核心處理
│   ├── modules_image_generator.gs    # AI 圖像生成
│   ├── modules_image_buttons.gs      # 圖片按鈕功能
│   ├── modules_image_push.gs         # 圖片推送
│   ├── modules_image_utils.gs        # 圖片工具
│   ├── modules_image_api.gs          # 圖片 API 整合
│   ├── modules_audio_handler.gs      # 語音處理
│   ├── modules_audio_advanced.gs     # 高級語音功能
│   └── modules_file_extractor.gs     # 檔案解析
├── 💾 數據管理
│   ├── modules_knowledge_base.gs     # 知識庫
│   ├── modules_note_memory.gs        # 筆記記憶
│   ├── modules_group_tracker.gs      # 群組追蹤
│   ├── modules_file_drive.gs         # Drive 文件管理
│   ├── modules_cloud_storage.gs      # 雲端存儲
│   └── modules_postback_manager.gs   # 按鈕數據管理
├── 🔧 工具模組
│   ├── modules_smart_responder.gs    # 智能響應器
│   ├── modules_smart_responder_utils.gs # 響應工具
│   ├── modules_reply_cost_saver.gs   # 回覆成本優化
│   ├── modules_voice_error_display.gs # 語音錯誤顯示
│   ├── modules_user_functions.gs     # 用戶功能
│   └── modules_oauth.gs              # OAuth 認證
├── 🧪 測試與部署
│   ├── _test/                        # 測試文件目錄
│   ├── _deployment/                  # 部署相關文件
│   └── _docs/                        # 文檔目錄
└── 📋 配置文件
    ├── appsscript.json              # Apps Script 配置
    ├── config.template.js           # 配置模板
    ├── .clasp.json                  # Clasp 配置
    └── .gitignore                   # Git 忽略文件
```

## 🔄 系統流程

### 主要處理流程
1. **LINE Webhook** → `modules_line_webhook.gs` (doPost)
2. **消息路由** → `core_main.gs` 
3. **類型判斷** → 文字/圖片/語音/檔案
4. **AI 處理** → `modules_ai_processor.gs`
5. **智能響應** → `modules_smart_responder.gs`
6. **回覆用戶** → LINE Bot API

### 數據流架構
- **Google Sheets** 作為主要數據庫
- **Google Drive** 存儲媒體文件
- **記憶系統** 維護用戶對話歷史
- **群組追蹤** 記錄群組互動

## 🛠️ 開發規範

### 代碼規範
- **模組化原則**：每個功能獨立模組，避免耦合
- **命名規範**：使用描述性函數名，不允許版本後綴
- **錯誤處理**：統一錯誤處理機制，禁止功能降級
- **最小修改**：直接修改現有函數，不創建新版本

### 文件結構規範
- **檔案註釋**：每個 .gs 文件必須包含檔案資訊註釋
- **功能分類**：core/modules/config 清晰分類
- **依賴管理**：明確標示模組間依賴關係
- **最後更新**：記錄文件最後修改日期

### QA 檢查流程
根據 `_docs/📖 04_開發與重構QA檢查清單.md` 嚴格執行：

#### 階段一：修改前準備
1. 執行 `codebase-retrieval` 搜索相關代碼
2. 執行 `preModificationCheck()` 檢查
3. 檢查 `core_module_manager.gs` 模組映射
4. 分析依賴關係和調用鏈
5. 提出修改策略

#### 階段二：修改後檢查
1. 更新文件 `@last-update` 日期
2. 確認函數名稱唯一性
3. 檢查跨模組影響

#### 階段三：最終驗證
1. 執行 `postModificationCheck()`
2. 執行 `runSafetyNetCheck()`
3. 執行 `validateModuleMapping()`
4. 清理廢棄代碼
5. 端對端功能測試

## 🔧 重要功能模組

### 核心系統
- **`core_main.gs`**：主要業務邏輯，版本管理，系統健康檢查
- **`config_core.gs`**：工作表管理，API 配置，功能分類系統
- **`core_module_manager.gs`**：模組依賴管理和映射

### AI 處理
- **`modules_ai_processor.gs`**：Gemini AI 核心處理邏輯
- **`modules_ai_features.gs`**：AI 功能整合和路由
- **`modules_ai_prompts.gs`**：提示詞管理系統

### 多媒體處理
- **`modules_image_core.gs`**：圖片分析和處理
- **`modules_image_generator.gs`**：AI 圖像生成
- **`modules_audio_handler.gs`**：語音合成和處理

### 群組功能
- **`modules_group_tracker.gs`**：群組聊天記錄和分析
- **`modules_line_processor_cmd.gs`**：群組指令處理

## 🔑 重要配置

### Google Sheets 工作表結構
- **APIKEY**：API 密鑰和模型配置
- **活動日誌**：系統操作記錄
- **用戶對話歷史**：對話記憶系統
- **群組發言記錄**：群組互動記錄
- **檔案記憶庫**：文件分析結果
- **儀表板**：系統統計和監控

### API 配置項目
- LINE Channel Access Token (必需)
- LINE Channel Secret (必需)
- Gemini API Key (必需)
- Google Drive Folder ID (必需)
- YouTube Data API Key (可選)
- Google Search API Key (可選)

### 功能分類模型配置
系統使用功能分類的 AI 模型管理，包括：
- **一般對話**：gemini-2.5-flash
- **圖像分析**：gemini-2.0-flash
- **語音合成**：gemini-2.5-flash-preview-tts
- **圖像生成**：gemini-2.0-flash-preview-image-generation

## 🧪 測試系統

### 測試原則
- 測試函數統一放在 `_test/` 目錄
- 使用繁體中文命名測試函數
- 長期測試函數使用 `_debug` 後綴
- 一次性測試函數使用 `_disposable` 後綴

### 核心測試函數
```javascript
// 系統健康檢查
systemHealthCheck()

// 快速功能測試
quickSystemTest()

// 模組映射驗證
validateModuleMapping()

// 功能分類系統測試
testFunctionalModelSystem_debug()
```

## 🚀 部署流程

### 自動部署
1. 代碼修改後使用 Git 推送到 GitHub
2. GitHub Actions 自動推送到 Google Apps Script
3. 只更新 Head 版本，不創建新版本或部署

### 手動部署
1. 使用 `clasp` 進行本地測試
2. 執行完整的 QA 檢查流程
3. 通過 Git 推送進行正式部署

## 💡 開發最佳實踐

### 功能開發
1. **先檢查現有代碼**：避免重複開發
2. **模組化設計**：新功能獨立成模組
3. **配置驅動**：使用配置文件管理功能開關
4. **錯誤處理**：統一的錯誤處理和日誌記錄

### 代碼維護
1. **定期清理**：移除廢棄代碼和註釋
2. **性能優化**：監控 API 使用和響應時間
3. **安全檢查**：敏感信息使用佔位符
4. **文檔更新**：及時更新相關文檔

### 調試技巧
1. **日誌追蹤**：使用 `console.log` 進行調試
2. **分段測試**：逐步測試複雜功能
3. **健康檢查**：定期執行系統健康檢查
4. **備份重要數據**：修改前備份關鍵工作表

## 🔗 相關資源

### 官方文檔
- [Google Apps Script 文檔](https://developers.google.com/apps-script)
- [LINE Bot API 文檔](https://developers.line.biz/)
- [Gemini AI API 文檔](https://ai.google.dev/docs)

### 專案資源
- **Apps Script 編輯器**：https://script.google.com/home/<USER>/1ZcH6Wms4VhjkddDZH3XRK-30UTOUBY1pZw0yKzLz_dI5928lJ0qovgoS/edit
- **Web 應用程式**：https://script.google.com/macros/s/AKfycbyXC-nDXi4_bzHba3Gd85JEd3gwqWxqSErShEVONzvVb6NzSqluRroXEjftmJoq1n-H/exec
- **GitHub 倉庫**：(需要設定)

### 開發工具
- **clasp**：Google Apps Script 命令行工具
- **Git**：版本控制系統
- **GitHub Actions**：自動部署流程

---

## 🎮 Claude Code 使用指南

### 常用指令
```bash
# 檢查專案狀態
cc "檢查當前專案的健康狀態和架構"

# 分析特定模組
cc "分析 modules_ai_processor.gs 模組的功能和依賴"

# 功能開發
cc "為群組功能增加新的統計分析功能"

# 問題診斷
cc "診斷為什麼語音合成功能無法正常工作"

# 代碼重構
cc "重構 image 相關模組，提升性能和可維護性"
```

### 最佳實踐
1. **具體描述需求**：明確說明要實現的功能
2. **提供上下文**：說明相關的模組和依賴
3. **指定測試要求**：要求提供相應的測試函數
4. **強調質量標準**：遵循專案的 QA 檢查清單

---

**注意事項**：
- 所有敏感資訊（API Keys、Tokens）使用佔位符表示
- 遵循專案的零容忍政策，不允許破壞現有功能
- 嚴格執行 QA 檢查清單中的所有步驟
- 保持模組化架構，避免緊耦合設計

此 initial.md 文件為 Claude Code 提供了全面的專案理解基礎，請根據具體需求進行功能開發和維護。
