// 活動日誌測試_debug.gs
// 🧪 測試活動日誌功能是否正常工作

/**
 * 🧪 測試活動日誌基本功能
 * 腳本：_test/活動日誌測試_debug.gs
 * 測試函數：testLogActivity_debug()
 */
function testLogActivity_debug() {
  console.log('🧪 === 測試活動日誌功能 ===');
  
  try {
    // 測試基本的 logActivity 調用
    console.log('📝 測試基本日誌記錄...');
    logActivity('System', '測試日誌功能', 'Success', 'test', 'testLogActivity_debug', '這是一個測試日誌記錄');
    
    console.log('✅ 基本日誌記錄測試完成');
    
    // 測試帶回應時間的日誌記錄
    console.log('📝 測試帶回應時間的日誌記錄...');
    logActivity('Reply', '測試回應時間', 'Success', 'test', 'testLogActivity_debug', '測試回應時間記錄', 150);
    
    console.log('✅ 回應時間日誌記錄測試完成');
    
    // 測試錯誤日誌記錄
    console.log('📝 測試錯誤日誌記錄...');
    logActivity('System', '測試錯誤日誌', 'Failure', 'error', 'testLogActivity_debug', '這是一個測試錯誤日誌');
    
    console.log('✅ 錯誤日誌記錄測試完成');
    
    return '📊 活動日誌測試完成！請檢查工作表"活動日誌"是否有新記錄。';
    
  } catch (error) {
    console.error('❌ 活動日誌測試失敗:', error);
    return `❌ 活動日誌測試失敗: ${error.message}`;
  }
}

/**
 * 🧪 測試模擬 LINE 消息處理流程
 */
function testLineMessageFlow_debug() {
  console.log('🧪 === 測試 LINE 消息處理流程 ===');
  
  try {
    // 模擬 webhook 接收消息
    console.log('📝 模擬 webhook 接收消息...');
    logActivity('Webhook', '收到訊息', 'Success', 'text', 'handleLineEvent', '來自user: testUser, 事件類型: message, 訊息類型: text');
    
    // 模擬 AI 處理
    console.log('📝 模擬 AI 處理...');
    logActivity('System', 'Gemini API 調用', 'Success', 'api', 'callGeminiAPI', '模型: gemini-2.5-flash, API版本: v1, 任務: general', 250);
    
    // 模擬回覆
    console.log('📝 模擬回覆...');
    logActivity('Reply', '文字回覆成功', 'Success', 'text', 'replyMessage', '訊息長度: 50字', 100);
    
    console.log('✅ LINE 消息處理流程測試完成');
    
    return '📊 LINE 消息處理流程測試完成！請檢查工作表"活動日誌"是否有完整的處理記錄。';
    
  } catch (error) {
    console.error('❌ LINE 消息處理流程測試失敗:', error);
    return `❌ LINE 消息處理流程測試失敗: ${error.message}`;
  }
}

/**
 * 🧪 檢查活動日誌工作表狀態
 */
function checkLogSheetStatus_debug() {
  console.log('🧪 === 檢查活動日誌工作表狀態 ===');
  
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('活動日誌');
    
    if (!sheet) {
      return '❌ 找不到活動日誌工作表！';
    }
    
    const lastRow = sheet.getLastRow();
    const lastColumn = sheet.getLastColumn();
    
    console.log(`📊 工作表狀態: ${lastRow} 行, ${lastColumn} 列`);
    
    if (lastRow > 1) {
      // 讀取最新的幾筆記錄
      const recentData = sheet.getRange(2, 1, Math.min(5, lastRow - 1), lastColumn).getValues();
      
      console.log('📋 最新記錄:');
      recentData.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row[0]} | ${row[1]} | ${row[3]} | ${row[4]}`);
      });
    }
    
    return `📊 活動日誌工作表狀態正常
• 總記錄數: ${lastRow - 1} 筆
• 欄位數: ${lastColumn} 個
• 最後更新: ${lastRow > 1 ? sheet.getRange(2, 1).getValue() : '無記錄'}`;
    
  } catch (error) {
    console.error('❌ 檢查活動日誌工作表失敗:', error);
    return `❌ 檢查活動日誌工作表失敗: ${error.message}`;
  }
}
