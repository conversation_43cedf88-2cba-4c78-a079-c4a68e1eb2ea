/*
 * 檔案: modules_ai_processor.gs
 * 分類: modules
 * 功能開關: -
 * 描述: AI-First 智能處理中樞增強模組
 * 依賴: [core_utils.gs, modules_audio_handler.gs]
 * 最後更新: 2025-07-15 - 重構語音功能：移除Native Audio Dialog，統一使用TTS模型
 */

// == AI-First 智能處理中樞增強模組 ==
// 支援語音、圖片生成、嵌入向量等所有功能模型
// 🎯 與 TextProcessor_AIFirst.gs 協同工作，提供擴展功能

/**
 * 🧠 增強版智能處理中樞 - 支援所有功能模型
 * 這是原有 smartProcessorHub 的增強版本，支援新增的功能模型
 */
function smartProcessorHubEnhanced(userInput, inputType = 'text', userId = null, sourceType = null) {
  try {
    console.log(`🧠 增強版智能處理中樞啟動: ${userInput.substring(0, 50)}...`);
    
    // 1. 使用統一提示詞系統識別用戶意圖
    const userIntent = callAIWithPrompt('INTENT_ANALYSIS', {
      userMessage: userInput,
      sourceType: sourceType || '個人對話'
    });
    
    let intentData;
    try {
      const jsonMatch = userIntent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        intentData = JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('意圖解析失敗:', parseError);
      intentData = { primary_intent: 'general_question' };
    }
    
    console.log(`🎯 識別意圖: ${intentData.primary_intent}`);
    
    // 2. 🚀 增強版智能路由 - 支援所有功能模型
    return routeByAIIntentEnhanced(intentData, userInput, userId, sourceType);
    
  } catch (error) {
    console.error('增強版智能處理中樞錯誤:', error);
    return handleAIProcessingError(userInput, null, userId, sourceType, error);
  }
}

/**
 * 🎯 增強版 AI 意圖路由系統 - 支援所有功能模型
 */
function routeByAIIntentEnhanced(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🎯 增強路由到意圖: ${intent.primary_intent} (信心度: ${intent.confidence}%)`);
    
    // 🔧 修復：強化語音聊天意圖檢測
    const isVoiceChatRequest = /跟我聊|聊聊|聊天|對話|語音聊天|語音對話|用語音|語音回應/i.test(originalMessage);

    switch (intent.primary_intent) {
      // 🔊 語音功能路由
      case 'text_to_speech':
        return handleAdvancedTTSRequest(intent, originalMessage, userId, sourceType);

      case 'conversational_audio':
      case 'voice_chat':
        return handleNativeAudioDialogRequest(intent, originalMessage, userId, sourceType);

      // 🔧 修復：捕獲被錯誤分類的語音聊天請求
      case 'casual_chat':
      case 'general_question':
        if (isVoiceChatRequest) {
          console.log(`🔧 修復：將 ${intent.primary_intent} 重新路由到語音對話`);
          return handleNativeAudioDialogRequest(intent, originalMessage, userId, sourceType);
        }
        // 否則繼續正常處理
        break;
        
      // 🎨 圖片生成路由
      case 'image_generation':
        return handleAdvancedImageGeneration(intent, originalMessage, userId, sourceType);
        
      // 📊 嵌入向量功能路由
      case 'semantic_search':
      case 'document_search':
      case 'file_similarity':
        return handleEmbeddingSearch(intent, originalMessage, userId, sourceType);
        
      // ⚡ 高效率處理路由
      case 'batch_processing':
      case 'quick_task':
      case 'bulk_operation':
        return handleFastProcessing(intent, originalMessage, userId, sourceType);
        
      // 🧠 其他意圖使用原有路由系統
      default:
        // 回退到原有的路由系統（沒有 replyToken，使用 null）
        return routeByAIIntent(intent, originalMessage, userId, sourceType, null);
    }
    
  } catch (error) {
    console.error('增強版 AI 路由錯誤:', error);
    return generateAIErrorResponse(intent, originalMessage, error);
  }
}

/**
 * 🔊 進階 TTS 處理 - 智能選擇鸚鵡模式或對話模式
 */
function handleAdvancedTTSRequest(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🔊 進階 TTS 處理: ${originalMessage}`);

    // 🧠 AI 判斷是鸚鵡模式還是對話模式
    const modeAnalysis = analyzeTTSMode(originalMessage);
    
    if (modeAnalysis.mode === 'parrot') {
      // 🦜 鸚鵡模式：直接複述
      return handleParrotModeTTS(modeAnalysis.textToSpeak, userId, sourceType);
    } else {
      // 🎙️ 對話模式：智能回應
      return handleConversationalAudioFallback(originalMessage, userId, sourceType);
    }
    
  } catch (error) {
    console.error('進階 TTS 處理失敗:', error);
    return '我理解您想要語音功能，但處理時遇到了問題。請稍後再試？';
  }
}

/**
 * 🧠 分析 TTS 模式 - 判斷用戶是要鸚鵡模式還是對話模式
 */
function analyzeTTSMode(message) {
  try {
    // 🎯 使用統一提示詞系統
    const analysis = callAIWithPrompt('TTS_MODE_ANALYSIS', {
      message: message
    });

    // 解析 AI 回應
    const jsonMatch = analysis.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    // 備用：簡單關鍵字判斷
    if (/你說|重複|複述|念出來|讀出來/.test(message)) {
      return {
        mode: 'parrot',
        textToSpeak: message.replace(/你說|重複|複述|念出來|讀出來|[：:]\s*/g, '').trim(),
        confidence: 80
      };
    } else if (/跟我聊|聊聊|聊天|對話|語音聊天|語音對話|用語音|語音回應/.test(message)) {
      return {
        mode: 'conversational',
        userQuery: message,
        confidence: 85
      };
    } else {
      return {
        mode: 'conversational',
        userQuery: message,
        confidence: 70
      };
    }
    
  } catch (error) {
    console.error('TTS 模式分析失敗:', error);
    return {
      mode: 'parrot',
      textToSpeak: message,
      confidence: 50
    };
  }
}

/**
 * 🦜 鸚鵡模式 TTS - 直接複述用戶指定的文字
 */
function handleParrotModeTTS(textToSpeak, userId, sourceType) {
  try {
    console.log(`🦜 鸚鵡模式 TTS: ${textToSpeak}`);
    
    if (!textToSpeak || textToSpeak.trim() === '') {
      return '🤔 我理解您想要語音轉換，但沒有找到要轉換的文字。請告訴我要說什麼？';
    }

    // 使用 TTS 模型
    const ttsResult = callGeminiTTS(textToSpeak);

    if (ttsResult.success) {
      logActivity('鸚鵡模式TTS', `用戶${userId}(${sourceType}): ${textToSpeak.substring(0, 50)}...`);

      return {
        type: 'audio_response',
        audioResult: ttsResult,
        originalText: textToSpeak,
        mode: 'parrot'
      };
    } else {
      return `❌ 語音轉換失敗：${ttsResult.error}\n\n💡 請稍後再試，或者換個方式表達`;
    }

  } catch (error) {
    console.error('鸚鵡模式 TTS 失敗:', error);
    return '語音轉換時遇到問題，請稍後再試？';
  }
}

/**
 * 🎙️ 對話音頻處理 - TTS 文字轉語音（對話模式）
 */
function handleNativeAudioDialogRequest(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🎙️ 對話音頻處理: ${originalMessage}`);

    // 🎯 調用對話音頻模型（TTS 對話模式）
    const audioDialogResult = callGeminiAudioDialog(originalMessage, {
      userId: userId,
      sourceType: sourceType,
      conversationHistory: [] // 可以添加對話歷史
    });

    if (audioDialogResult.success) {
      logActivity('對話音頻', `用戶${userId}(${sourceType}): ${originalMessage.substring(0, 50)}...`);

      return {
        type: 'audio_response',
        audioResult: audioDialogResult,
        originalText: audioDialogResult.originalText,
        userQuery: originalMessage,
        mode: 'conversational_tts'
      };
    } else {
      throw new Error(`對話音頻處理失敗: ${audioDialogResult.error}`);
    }

  } catch (error) {
    console.error('對話音頻處理失敗:', error);
    return `❌ 對話音頻功能故障

🔧 技術詳情：
• 錯誤: ${error.message}
• 用戶輸入: "${originalMessage}"

📞 請將此錯誤訊息回報給開發團隊`;
  }
}



/**
 * 🎨 進階圖片生成處理
 */
function handleAdvancedImageGeneration(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🎨 進階圖片生成處理: ${originalMessage}`);

    // 提取並優化圖像描述
    const enhancedPrompt = enhanceImagePrompt(originalMessage);

    // 使用圖片生成模型
    const imageResult = callGeminiImageGeneration(enhancedPrompt);

    if (imageResult.success) {
      logActivity('進階圖片生成', `用戶${userId}(${sourceType}): ${enhancedPrompt.substring(0, 50)}...`);

      return {
        type: 'image_response',
        imageResult: imageResult,
        originalPrompt: enhancedPrompt,
        userInput: originalMessage
      };
    } else {
      return `❌ 圖片生成失敗：${imageResult.error}\n\n💡 請稍後再試，或者換個描述方式`;
    }

  } catch (error) {
    console.error('進階圖片生成處理失敗:', error);
    return '我理解您想要生成圖片，但處理時遇到了問題。請稍後再試？';
  }
}

/**
 * 🎨 增強圖像提示詞
 */
function enhanceImagePrompt(originalMessage) {
  try {
    console.log(`🎨 [提示詞增強] 開始增強圖像提示詞`);
    console.log(`📝 [輸入] 原始提示詞: "${originalMessage}"`);

    // 🎯 使用統一提示詞系統優化圖像描述
    console.log(`🤖 [處理] 使用 IMAGE_PROMPT_ENHANCEMENT 提示詞進行AI增強...`);
    const enhanced = callAIWithPrompt('IMAGE_PROMPT_ENHANCEMENT', {
      originalPrompt: originalMessage
    });

    console.log(`📤 [輸出] AI增強結果: "${enhanced}"`);
    console.log(`🔄 [對比] 原始: "${originalMessage}" → 增強後: "${enhanced.trim() || originalMessage}"`);

    return enhanced.trim() || originalMessage;

  } catch (error) {
    console.error('❌ [錯誤] 圖像提示詞增強失敗:', error);
    console.log(`📤 [備用輸出] 返回原始提示詞: "${originalMessage}"`);
    return originalMessage;
  }
}

/**
 * 📊 嵌入向量搜索處理
 */
function handleEmbeddingSearch(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`📊 嵌入向量搜索處理: ${originalMessage}`);
    
    // 🚀 使用嵌入模型進行語義搜索
    const embeddingResult = callGeminiEmbedding(originalMessage);
    
    if (embeddingResult.success) {
      // 🔍 在檔案記憶庫中進行語義搜索
      const searchResults = performSemanticSearch(embeddingResult.embedding, userId);
      
      if (searchResults.length > 0) {
        logActivity('語義搜索', `用戶${userId}(${sourceType}): 找到 ${searchResults.length} 個相關結果`);
        return generateSemanticSearchResponse(searchResults, originalMessage);
      } else {
        return `🔍 語義搜索完成，但沒有找到與「${originalMessage}」相關的內容。\n\n💡 您可以：\n• 換個關鍵字試試\n• 上傳一些檔案建立記憶庫\n• 詢問其他問題`;
      }
    } else {
      // 🔄 備用：使用傳統檔案查詢
      return handleFileReference(userId, originalMessage, sourceType);
    }
    
  } catch (error) {
    console.error('嵌入向量搜索處理失敗:', error);
    return handleFileReference(userId, originalMessage, sourceType);
  }
}

/**
 * 🔍 執行語義搜索（簡化版本）
 */
function performSemanticSearch(queryEmbedding, userId) {
  try {
    // 🚀 這裡需要實現實際的語義搜索邏輯
    // 暫時返回模擬結果
    console.log(`🔍 執行語義搜索 for 用戶 ${userId}`);
    
    // 模擬搜索結果
    return [
      { fileName: '範例檔案1.pdf', similarity: 0.85, content: '模擬內容...' },
      { fileName: '範例檔案2.docx', similarity: 0.78, content: '模擬內容...' }
    ];
    
  } catch (error) {
    console.error('執行語義搜索失敗:', error);
    return [];
  }
}

/**
 * 📝 生成語義搜索回應
 */
function generateSemanticSearchResponse(searchResults, originalQuery) {
  try {
    // 🎯 使用統一提示詞系統
    const response = callAIWithPrompt('SEMANTIC_SEARCH_RESPONSE', {
      query: originalQuery,
      resultCount: searchResults.length,
      topResults: searchResults.slice(0, 3).map(r => r.fileName).join(', ')
    });

    let fullResponse = response + '\n\n📋 相關檔案：\n';
    
    searchResults.slice(0, 5).forEach((result, index) => {
      const similarityPercent = Math.round(result.similarity * 100);
      fullResponse += `${index + 1}. ${result.fileName} (相似度: ${similarityPercent}%)\n`;
    });
    
    if (searchResults.length > 5) {
      fullResponse += `\n... 還有 ${searchResults.length - 5} 個相關結果`;
    }
    
    return fullResponse;

  } catch (error) {
    console.error('生成語義搜索回應失敗:', error);
    return `🔍 找到 ${searchResults.length} 個相關檔案，但無法生成詳細說明。`;
  }
}

/**
 * ⚡ 高效率處理 - 使用快速模型進行批量任務
 */
function handleFastProcessing(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`⚡ 高效率處理: ${originalMessage}`);
    
    // 🧠 AI 分析任務類型
    const taskAnalysis = analyzeTaskForFastProcessing(originalMessage);
    
    switch (taskAnalysis.taskType) {
      case 'bulk_summary':
        return handleBulkSummary(taskAnalysis, userId, sourceType);
        
      case 'quick_classification':
        return handleQuickClassification(taskAnalysis, userId, sourceType);
        
      case 'batch_translation':
        return handleBatchTranslation(taskAnalysis, userId, sourceType);
        
      default:
        // 使用高效率模型處理一般任務
        return handleGeneralFastTask(originalMessage, userId, sourceType);
    }
    
  } catch (error) {
    console.error('高效率處理失敗:', error);
    return '高效率處理時遇到問題，請稍後再試？';
  }
}

/**
 * 🧠 分析快速處理任務類型
 */
function analyzeTaskForFastProcessing(message) {
  try {
    // 簡單的任務類型推測
    if (/批量|大量|所有.*摘要|整理.*檔案/.test(message)) {
      return { taskType: 'bulk_summary', content: message };
    } else if (/分類|歸類|整理/.test(message)) {
      return { taskType: 'quick_classification', content: message };
    } else if (/翻譯|translate/.test(message)) {
      return { taskType: 'batch_translation', content: message };
    } else {
      return { taskType: 'general_fast', content: message };
    }
    
  } catch (error) {
    console.error('任務分析失敗:', error);
    return { taskType: 'general_fast', content: message };
  }
}

/**
 * 📚 批量摘要處理
 */
function handleBulkSummary(taskAnalysis, userId, sourceType) {
  console.log(`📚 批量摘要處理: ${taskAnalysis.content}`);
  
  // 🚀 使用高效率模型進行批量摘要
  const config = getConfig();
  const fastModel = config.fastModel;
  
  return `📚 批量摘要功能啟動！\n\n🎯 使用模型：${fastModel}\n📝 任務：${taskAnalysis.content}\n\n⚡ 批量摘要功能開發中，敬請期待！`;
}

/**
 * 🗂️ 快速分類處理
 */
function handleQuickClassification(taskAnalysis, userId, sourceType) {
  console.log(`🗂️ 快速分類處理: ${taskAnalysis.content}`);
  
  const config = getConfig();
  const fastModel = config.fastModel;
  
  return `🗂️ 快速分類功能啟動！\n\n🎯 使用模型：${fastModel}\n📝 任務：${taskAnalysis.content}\n\n⚡ 快速分類功能開發中，敬請期待！`;
}

/**
 * 🌐 批量翻譯處理
 */
function handleBatchTranslation(taskAnalysis, userId, sourceType) {
  console.log(`🌐 批量翻譯處理: ${taskAnalysis.content}`);
  
  const config = getConfig();
  const fastModel = config.fastModel;
  
  return `🌐 批量翻譯功能啟動！\n\n🎯 使用模型：${fastModel}\n📝 任務：${taskAnalysis.content}\n\n⚡ 批量翻譯功能開發中，敬請期待！`;
}

/**
 * ⚡ 一般快速任務處理
 */
function handleGeneralFastTask(originalMessage, userId, sourceType) {
  try {
    console.log(`⚡ 一般快速任務: ${originalMessage}`);
    
    // 使用高效率模型快速回應
    const fastResponse = callGemini(originalMessage, 'general', null, 'fast_processing');
    
    logActivity('快速處理', `用戶${userId}(${sourceType}): ${originalMessage.substring(0, 50)}...`);
    
    return `⚡ 快速處理完成！\n\n${fastResponse}`;
    
  } catch (error) {
    console.error('一般快速任務處理失敗:', error);
    return '快速處理時遇到問題，請稍後再試？';
  }
}
