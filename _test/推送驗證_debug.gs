// 推送驗證_debug.gs
// 🧪 驗證代碼是否成功推送到 Google Apps Script

/**
 * 🧪 驗證推送成功
 * 腳本：_test/推送驗證_debug.gs
 * 測試函數：verifyPushSuccess_debug()
 */
function verifyPushSuccess_debug() {
  console.log('🧪 === 驗證推送成功 ===');
  
  try {
    // 檢查當前時間戳
    const currentTime = new Date().toLocaleString('zh-TW');
    console.log(`📅 當前時間: ${currentTime}`);
    
    // 檢查核心函數是否存在
    const coreFunction = typeof callGeminiTTS === 'function';
    console.log(`🔧 callGeminiTTS 函數: ${coreFunction ? '存在' : '不存在'}`);
    
    // 檢查修復的函數是否存在
    const audioFunction = typeof callGeminiAudioDialog === 'function';
    console.log(`🎙️ callGeminiAudioDialog 函數: ${audioFunction ? '存在' : '不存在'}`);
    
    // 檢查處理函數是否存在
    const handlerFunction = typeof handleParrotModeTTS === 'function';
    console.log(`🔊 handleParrotModeTTS 函數: ${handlerFunction ? '存在' : '不存在'}`);
    
    // 檢查日誌函數是否存在
    const logFunction = typeof logActivity === 'function';
    console.log(`📝 logActivity 函數: ${logFunction ? '存在' : '不存在'}`);
    
    return `✅ 推送驗證完成！
    
📊 驗證結果:
• 時間戳: ${currentTime}
• callGeminiTTS: ${coreFunction ? '✅' : '❌'}
• callGeminiAudioDialog: ${audioFunction ? '✅' : '❌'}
• handleParrotModeTTS: ${handlerFunction ? '✅' : '❌'}
• logActivity: ${logFunction ? '✅' : '❌'}

🎯 推送狀態: ${coreFunction && audioFunction && handlerFunction && logFunction ? '✅ 成功' : '❌ 部分失敗'}`;
    
  } catch (error) {
    console.error('❌ 推送驗證失敗:', error);
    return `❌ 推送驗證失敗: ${error.message}`;
  }
}

/**
 * 🧪 測試基本功能
 */
function testBasicFunctions_debug() {
  console.log('🧪 === 測試基本功能 ===');
  
  try {
    let results = '📊 基本功能測試結果:\n\n';
    
    // 測試 1: 配置讀取
    try {
      const config = getConfig();
      results += `✅ 配置讀取: 成功\n`;
      results += `   TTS模型: ${config.ttsModel || '未設定'}\n`;
      results += `   對話音頻模型: ${config.audioDialogModel || '未設定'}\n\n`;
    } catch (configError) {
      results += `❌ 配置讀取: 失敗 - ${configError.message}\n\n`;
    }
    
    // 測試 2: 日誌記錄
    try {
      logActivity('System', '推送驗證測試', 'Success', 'test', 'testBasicFunctions_debug', '這是一個推送驗證測試');
      results += `✅ 日誌記錄: 成功\n\n`;
    } catch (logError) {
      results += `❌ 日誌記錄: 失敗 - ${logError.message}\n\n`;
    }
    
    // 測試 3: 模型配置檢查
    try {
      const modelConfig = getModelConfig('gemini-2.5-flash-preview-tts');
      results += `✅ 模型配置檢查: 成功\n`;
      results += `   API版本: ${modelConfig?.apiVersion || '未知'}\n\n`;
    } catch (modelError) {
      results += `❌ 模型配置檢查: 失敗 - ${modelError.message}\n\n`;
    }
    
    results += `📅 測試時間: ${new Date().toLocaleString('zh-TW')}`;
    
    console.log(results);
    return results;
    
  } catch (error) {
    console.error('❌ 基本功能測試失敗:', error);
    return `❌ 基本功能測試失敗: ${error.message}`;
  }
}
